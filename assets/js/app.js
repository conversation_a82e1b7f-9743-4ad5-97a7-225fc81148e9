// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Register GSAP plugins
import { gsap } from "gsap"
import { ScrollToPlugin } from "gsap/ScrollToPlugin"
import "@alenaksu/json-viewer"

// Import modular components
import {
  BrokerTabsSortable,
  ProtocolSelector,
  ConnectionTableAutoAnimate,
  TraceTableGSAP,
  TraceRowHighlight,
  JsonViewer,
  TraceSlickGrid,
  PayloadEditor,
  UnifiedPayloadEditor,
  FileUpload,
  GlobalKeyboardShortcuts,
  ShortcutRecorder
} from "./hooks"
import { initializeGlobalEventHandlers } from "./global-events"
import { initializeGlobalUtilities, initializeDOMContentLoaded } from "./utils"
import { initializeLiveSocket, initializeDevelopmentFeatures } from "./livesocket-config"

// Register GSAP plugins
gsap.registerPlugin(ScrollToPlugin)

const csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")

// Define hooks using imported modules
const Hooks = {
  BrokerTabsSortable,
  ProtocolSelector,
  ConnectionTableAutoAnimate,
  TraceTableGSAP,
  TraceRowHighlight,
  JsonViewer,
  TraceSlickGrid,
  PayloadEditor,
  UnifiedPayloadEditor,
  FileUpload,
  GlobalKeyboardShortcuts,
  ShortcutRecorder
};

// Initialize all modules
initializeGlobalEventHandlers();
initializeGlobalUtilities();
initializeDOMContentLoaded();

// Initialize LiveSocket with hooks
const liveSocket = initializeLiveSocket(csrfToken, Hooks);

// Initialize development features
initializeDevelopmentFeatures();
